import { forwardRef, useImperativeHandle } from 'react';
import { Spin, Empty, Button } from 'antd';
import useInfiniteScroll from 'HOOKS/useInfiniteScroll';
import styles from './InfiniteScrollContainer.module.less';
// 无限滚动容器

const InfiniteScrollContainer = (
    {
        fetchData, // 获取数据的函数
        renderItem, // 渲染单个项目的函数
        renderEmpty, // 渲染空状态的函数（可选）
        renderError, // 渲染错误状态的函数（可选）
        scrollOptions = {}, // 滚动配置选项
        className = '',
        containerStyle = {}, // 容器样式
        contentStyle = {},
        loadingText = '加载中...',
        noMoreText = '没有更多数据了',
        showLoadingIndicator = true,
        loadingIndicator,
        noMoreIndicator, // 自定义没有更多数据
        gridClassName = '',
        ...restProps
    },
    ref
) => {
    const { data, loading, hasMore, total, error, containerRef, refresh, updateParams } =
        useInfiniteScroll(fetchData, scrollOptions);

    useImperativeHandle(
        ref,
        () => ({
            refresh,
            updateParams,
            data,
            total,
            loading,
            hasMore
        }),
        [refresh, updateParams, data, total, loading, hasMore]
    );

    // 加载
    const renderLoadingIndicator = () => {
        if (!showLoadingIndicator || !loading) {
            return null;
        }

        if (loadingIndicator) {
            return loadingIndicator;
        }

        return (
            <div className={styles.loadingIndicator}>
                <Spin size="small" />
                <span className={styles.loadingText}>{loadingText}</span>
            </div>
        );
    };

    // 没有更多数据
    const renderNoMoreIndicator = () => {
        if (hasMore || data.length === 0) {
            return null;
        }

        if (noMoreIndicator) {
            return noMoreIndicator;
        }

        return (
            <div className={styles.noMoreIndicator}>
                <span className={styles.noMoreText}>{noMoreText}</span>
            </div>
        );
    };

    // 错误状态
    if (error && data?.length === 0) {
        if (renderError) {
            return renderError(error, refresh);
        }
        return (
            <div className={styles.errorContainer}>
                <div className={styles.errorMessage}>加载失败，请重试</div>
                {/* <Button onClick={() => refresh()} className={styles.retryButton}>
                        重试
                    </Button> */}
            </div>
        );
    }

    // 空状态
    if (!loading && data.length === 0) {
        if (renderEmpty) {
            return renderEmpty();
        }
        return (
            <div className={styles.emptyContainer}>
                <Empty description="暂无数据" />
            </div>
        );
    }

    return (
        <div
            ref={containerRef}
            className={`${styles.infiniteScrollContainer} ${className}`}
            style={containerStyle}
            {...restProps}
        >
            <div className={`${styles.content} ${gridClassName}`} style={contentStyle}>
                {data.map((item, index) => renderItem(item, index, { refresh, updateParams }))}
            </div>

            {renderLoadingIndicator()}
            {renderNoMoreIndicator()}
        </div>
    );
};

export default forwardRef(InfiniteScrollContainer);
