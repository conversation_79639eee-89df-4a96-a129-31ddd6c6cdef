// 基础样式 - 移除，因为已经在列表项样式中处理了hover效果

.documentListCard {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.listMainContainer {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.listScrollContainer {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

// 表头样式 - 参考参考页面的表格样式
.tableHeader {
  display: flex;
  padding: 8px 20px;
  background-color: #fafafa;
  border-bottom: 1px solid #f0f0f0;
  font-weight: 500;
  font-size: 14px;
  color: #666;
  position: sticky;
  top: 0;
  z-index: 1;
}

.tableHeaderId {
  width: 40px;
  text-align: center;
}

.tableHeaderTitle {
  flex: 1;
}

.tableHeaderType {
  width: 60px;
  text-align: center;
}

.tableHeaderStatus {
  width: 80px;
  text-align: center;
}

.tableHeaderActions {
  width: 40px;
}

// 文档列表项样式 - 优化为更清晰的表格式布局
.documentListItem {
  cursor: pointer;
  padding: 8px 20px;
  border-radius: 6px;
  margin-bottom: 2px;
  border: none;
  transition: all 0.2s ease;
  font-size: 14px;
  line-height: 1.4;
}

.documentListItemNormal {
  background-color: transparent;
  color: #333333;
  box-shadow: none;
}

.documentListItemNormal:hover {
  background-color: #f5f5f5;
}

.documentListItemSelected {
  background-color: #1677ff;
  color: #ffffff;
  box-shadow: 0 2px 8px rgba(22, 119, 255, 0.3);
}

.documentListItemSelected:hover {
  background-color: #1677ff;
}

// 文档项内容布局 - 采用表格式对齐
.documentItemContent {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 8px;
}

// 文档标题样式 - 优化显示效果
.documentItemTitle {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 14px;
  line-height: 1.4;
}

.documentItemTitleNormal {
  color: #333333;
  font-weight: 400;
}

.documentItemTitleSelected {
  color: #ffffff;
  font-weight: 500;
}

// ID徽章样式 - 优化视觉效果，参考参考页面的简洁风格
.documentIdBadge {
  background-color: #6fa3f5a6;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid #6fa3f5;
  min-width: 30px;
  text-align: center;
  display: inline-block;
}

// 右侧操作区域 - 优化布局
.documentItemRight {
  display: flex;
  align-items: center;
  gap: 8px;
}

// 类型标签样式 - 优化为更简洁的样式
.documentTypeTag {
  min-width: 50px;
  text-align: center;
  display: inline-block;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 4px;
}

// 下拉菜单项样式
.dropdownItemDisable {
  color: #faad14;
}

.dropdownItemEnable {
  color: #52c41a;
}

.dropdownItemDelete {
  color: #ff4d4f;
}

.dropdownIconMargin {
  margin-right: 8px;
}

// 更多操作图标样式
.moreIcon {
  color: #999999;
  font-size: 16px;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.moreIcon:hover {
  background-color: #f5f5f5;
  color: #666666;
}

.moreIconSelected {
  color: #ffffff;
}

.moreIconSelected:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

// 分页容器样式
.paginationContainer {
  display: flex;
  justify-content: center;
  padding: 16px 0;
  border-top: 1px solid #f0f0f0;
  margin-top: 8px;
}

// 响应式优化 - 在小屏幕上调整布局
@media (max-width: 768px) {
  .tableHeader {
    padding: 6px 12px;
    font-size: 12px;
  }

  .documentListItem {
    padding: 6px 12px;
    font-size: 13px;
  }

  .documentIdBadge {
    font-size: 11px;
    padding: 1px 4px;
  }

  .documentTypeTag {
    font-size: 11px;
    padding: 1px 6px;
  }
}
