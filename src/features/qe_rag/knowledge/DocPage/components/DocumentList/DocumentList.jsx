import React, { forwardRef } from 'react';
import { Card, List, Tag, Tooltip, Dropdown, Pagination } from 'antd';
import { MoreOutlined, StopOutlined, PlayCircleOutlined, DeleteOutlined } from '@ant-design/icons';
import DocumentSearchFilter from '../DocumentSearchFilter/DocumentSearchFilter';
import styles from './DocumentList.module.less';

const DocumentList = forwardRef(
    (
        {
            // 搜索筛选相关
            searchText,
            onSearchChange,
            filters,
            onFilterChange,
            onResetFilters,
            onApplyFilters,
            hasActiveFilters,
            statuses,

            // 列表数据相关
            items,
            loading,
            selectedDoc,
            onDocumentSelect,

            // 分页相关
            currentPage,
            pageSize,
            total,
            onPageChange,

            // 操作相关
            onUpdateDocumentStatus,
            onDeleteDocument,
            getStatusTag
        },
        ref
    ) => {
        // 获取下拉菜单项
        const getDropdownItems = (item) => [
            // 禁用选项 - 只有切片成功(status=2)的文档才显示
            ...(item.status === 2
                ? [
                      {
                          key: 'disable',
                          label: (
                              <span className={styles.dropdownItemDisable}>
                                  <StopOutlined className={styles.dropdownIconMargin} />
                                  禁用
                              </span>
                          ),
                          onClick: () => {
                              onUpdateDocumentStatus(
                                  item.id,
                                  item.title,
                                  4, // 状态4表示已禁用
                                  '禁用'
                              );
                          }
                      }
                  ]
                : []),
            // 启用选项 - 只有已禁用(status=4)的文档才显示
            ...(item.status === 4
                ? [
                      {
                          key: 'enable',
                          label: (
                              <span className={styles.dropdownItemEnable}>
                                  <PlayCircleOutlined className={styles.dropdownIconMargin} />
                                  启用
                              </span>
                          ),
                          onClick: () => {
                              onUpdateDocumentStatus(
                                  item.id,
                                  item.title,
                                  2, // 状态2表示已启用
                                  '启用'
                              );
                          }
                      }
                  ]
                : []),
            // 删除选项 - 所有文档都显示
            {
                key: 'delete',
                label: (
                    <span className={styles.dropdownItemDelete}>
                        <DeleteOutlined className={styles.dropdownIconMargin} />
                        删除
                    </span>
                ),
                onClick: () => {
                    onDeleteDocument(item.id, item.title);
                }
            }
        ];

        return (
            <Card
                title={
                    <DocumentSearchFilter
                        searchText={searchText}
                        onSearchChange={onSearchChange}
                        filters={filters}
                        onFilterChange={onFilterChange}
                        onResetFilters={onResetFilters}
                        onApplyFilters={onApplyFilters}
                        hasActiveFilters={hasActiveFilters}
                        statuses={statuses}
                    />
                }
                className={styles.documentListCard}
                styles={{
                    header: {
                        padding: '16px 16px 8px 16px'
                    },
                    body: {
                        padding: '8px 16px 16px 16px',
                        flex: 1,
                        overflow: 'hidden',
                        display: 'flex',
                        flexDirection: 'column'
                    }
                }}
            >
                <div className={styles.listMainContainer}>
                    {/* 文档列表 */}
                    <div ref={ref} className={styles.listScrollContainer}>
                        {/* 表头 */}
                        <div className={styles.tableHeader}>
                            <div className={styles.tableHeaderId}>ID</div>
                            <div className={styles.tableHeaderTitle}>文档名</div>
                            <div className={styles.tableHeaderType}>类型</div>
                            <div className={styles.tableHeaderStatus}>状态</div>
                            <div className={styles.tableHeaderActions}></div>
                        </div>

                        <List
                            loading={loading}
                            dataSource={items}
                            renderItem={(item, index) => {
                                const safeItem = {
                                    id: item.id || `item-${index}`,
                                    title: item.title || '未命名文档',
                                    type: item.type || '未知类型',
                                    status: item.status || 1,
                                    owner: item.owner || '未知',
                                    createAt: item.createAt || '未知时间'
                                };

                                return (
                                    <List.Item
                                        key={safeItem.id}
                                        onClick={() => onDocumentSelect(safeItem)}
                                        className={`${styles.documentListItem} ${
                                            selectedDoc?.id === safeItem.id
                                                ? `${styles.documentListItemSelected} ${styles.documentItemSelected}`
                                                : `${styles.documentListItemNormal} ${styles.documentItem}`
                                        }`}
                                    >
                                        <div className={styles.documentItemContent}>
                                            {/* ID列 */}
                                            <div style={{ width: '40px', textAlign: 'center' }}>
                                                <span className={styles.documentIdBadge}>
                                                    {safeItem.id}
                                                </span>
                                            </div>

                                            {/* 文档名列 */}
                                            <div style={{ flex: 1, paddingLeft: '8px' }}>
                                                <Tooltip title={safeItem.title} placement="top">
                                                    <div
                                                        className={`${
                                                            styles.documentItemTitle
                                                        } ${
                                                            selectedDoc?.id === safeItem.id
                                                                ? styles.documentItemTitleSelected
                                                                : styles.documentItemTitleNormal
                                                        }`}
                                                    >
                                                        {safeItem.title}
                                                    </div>
                                                </Tooltip>
                                            </div>

                                            {/* 类型列 */}
                                            <div style={{ width: '60px', textAlign: 'center' }}>
                                                <Tag
                                                    color={'blue'}
                                                    className={styles.documentTypeTag}
                                                >
                                                    {safeItem.type}
                                                </Tag>
                                            </div>

                                            {/* 状态列 */}
                                            <div style={{ width: '80px', textAlign: 'center' }}>
                                                {getStatusTag(
                                                    safeItem.status,
                                                    selectedDoc?.id === safeItem.id
                                                )}
                                            </div>

                                            {/* 操作列 */}
                                            <div style={{ width: '40px', textAlign: 'center' }}>
                                                <Dropdown
                                                    menu={{
                                                        items: getDropdownItems(safeItem)
                                                    }}
                                                    trigger={['click']}
                                                    placement="bottomRight"
                                                    getPopupContainer={() => document.body}
                                                >
                                                    <div
                                                        onClick={(e) => e.stopPropagation()}
                                                        className={
                                                            selectedDoc?.id === safeItem.id
                                                                ? styles.moreIconSelected
                                                                : styles.moreIcon
                                                        }
                                                    >
                                                        <MoreOutlined />
                                                    </div>
                                                </Dropdown>
                                            </div>
                                        </div>
                                    </List.Item>
                                );
                            }}
                        />
                    </div>

                    {/* 分页 - 只有多页时才显示 */}
                    {total > pageSize && (
                        <div className={styles.paginationContainer}>
                            <Pagination
                                current={currentPage}
                                pageSize={pageSize}
                                total={total}
                                onChange={onPageChange}
                                simple
                                size="small"
                            />
                        </div>
                    )}
                </div>
            </Card>
        );
    }
);

DocumentList.displayName = 'DocumentList';

export default DocumentList;
