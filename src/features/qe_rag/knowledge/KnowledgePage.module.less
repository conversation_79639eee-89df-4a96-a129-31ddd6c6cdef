.container {
    height: 100vh;
    background: #f3f2fe87;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* 固定头部区域 */
.fixedHeader {
    flex-shrink: 0;
    padding: 24px 24px 0 24px;
     background: #f3f2fe53;
    backdrop-filter: blur(10px);
}

/* 可滚动内容区域 */
.scrollableContent {
    flex: 1;
    overflow-y: auto;
    padding: 0 16px;
}


/* 搜索栏 */
.searchBar {
    margin-top: 16px;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.searchInput {
    max-width: 320px;
    height: 36px !important;
    border-radius: 12px;
    border: 1px solid rgba(96, 92, 229, 0.2);
    background: #ffffff;

    // 针对有prefix的Input组件
    :global(.ant-input-affix-wrapper) {
        height: 36px !important;
        border: none !important;
        box-shadow: none !important;
        padding: 0 12px !important;
        display: flex !important;
        align-items: center !important;
        background: transparent !important;
        border-radius: 12px !important;
    }

    :global(.ant-input) {
        border: none !important;
        box-shadow: none !important;
        font-size: 14px !important;
        color: #333 !important;
        background: transparent !important;
        height: auto !important;
        line-height: 1.5 !important;
        padding: 0 !important;
    }

    :global(.ant-input-prefix) {
        margin-right: 8px !important;
        display: flex !important;
        align-items: center !important;
    }

    // :global(.ant-input:focus) {
    //     border-color: #605ce5;
    //     box-shadow: 0 0 0 3px rgba(96, 92, 229, 0.1);
    // }

    // 确保搜索框 placeholder 颜色
    // :global(.ant-input::placeholder) {
    //     color: #a5a3d9 !important;
    //     opacity: 1 !important;
    // }
}

.workGroupSelect {
    height: 36px !important;

    :global(.ant-select-selector) {
        height: 36px !important;
        border-radius: 12px !important;
        border: 1px solid rgba(96, 92, 229, 0.2) !important;
        background: #ffffff !important;
        padding: 0 12px !important;
        box-shadow: none !important;
        display: flex !important;
        align-items: center !important;
        line-height: 1.5 !important;
    }

    :global(.ant-select-selection-item) {
        line-height: 1.5 !important;
        font-size: 14px !important;
        color: #333 !important;
        height: auto !important;
        display: flex !important;
        align-items: center !important;
        padding: 0 !important;
        margin: 0 !important;
    }

    :global(.ant-select-selection-placeholder) {
        line-height: 1.5 !important;
        font-size: 14px !important;
        height: auto !important;
        display: flex !important;
        align-items: center !important;
        padding: 0 !important;
        margin: 0 !important;
        // color: #a5a3d9 !important;
    }

    :global(.ant-select-arrow) {
        display: flex !important;
        align-items: center !important;
    }

    // // 确保 placeholder 颜色正确
    // :global(.ant-select:not(.ant-select-customize-input) .ant-select-selector .ant-select-selection-placeholder) {
    //     color: #a5a3d9 !important;
    //     opacity: 1 !important;
    // }

    // :global(.ant-select-arrow) {
    //     color: #605ce5 !important;
    // }

    // &:hover {
    //     :global(.ant-select-selector) {
    //         border-color: #605ce5 !important;
    //     }
    // }

    :global(.ant-select-focused) {
        :global(.ant-select-selector) {
            border-color: #605ce5 !important;
            box-shadow: 0 0 0 3px rgba(96, 92, 229, 0.1) !important;
        }
    }

    // 确保下拉框样式一致
    :global(.ant-select-dropdown) {
        border-radius: 12px !important;
        border: 1px solid rgba(96, 92, 229, 0.2) !important;
        box-shadow: 0 8px 24px rgba(96, 92, 229, 0.15) !important;
        background: #ffffff !important;
    }
}

/* 卡片网格 */
.cardGrid {
    // display: grid;
    // grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    // gap: 16px;
    // margin-bottom: 24px;
    display: flex;
    flex-wrap: wrap;
}

/* 知识库卡片 */
.knowledgeCard {
    border-radius: 16px;
    border: 1px solid rgba(96, 92, 229, 0.15);
    background: #ffffff;
    box-shadow: 0 4px 16px rgba(96, 92, 229, 0.08);
    transition: all 0.3s ease;
    cursor: pointer;
    overflow: hidden;
    height: 180px;
    margin: 8px;
    width: calc(100% / 5 - 24px);
    &:hover {
        box-shadow: 0 12px 32px rgba(96, 92, 229, 0.15);
        transform: translateY(-3px);
        border-color: rgba(96, 92, 229, 0.25);
    }

    :global(.ant-card-body) {
        padding: 0;
        height: 100%;
    }
}

.cardContent {
    padding: 20px;
    height: 100%;
    display: flex;
    flex-direction: column;
    background: linear-gradient(135deg, #ffffff 0%, #fefefe 100%);
}

.cardHeader {
    display: flex;
    align-items: flex-start;
    gap: 10px;
    margin-bottom: 12px;
    position: relative;
}

.cardIcon {
    width: 44px;
    height: 44px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.iconText {
    color: #ffffff;
    font-size: 18px;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.cardTitle {
    flex: 1;
    min-width: 0;
}

.knowledgeName {
    margin: 0 0 4px 0;
    font-size: 16px;
    font-weight: 600;
    color: #111827;
    line-height: 1.3;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.cardId {
    font-size: 10px;
    color: #605ce5;
    font-weight: 500;
    background: rgba(96, 92, 229, 0.1);
    padding: 2px 6px;
    border-radius: 6px;
    display: inline-block;
}

.cardDescription {
    margin: 10px 0;
    font-size: 12px;
    color: #666;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    min-height: 16px;
}

.cardFooter {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    margin-top: auto;
    padding-top: 12px;
}

.tags {
    display: flex;
    gap: 6px;
    flex-wrap: wrap;
}

.tag {
    font-size: 11px;
    color: #666;
    background: #f5f7fa;
    padding: 2px 6px;
    border-radius: 3px;
    white-space: nowrap;
}

.updateTime {
    font-size: 11px;
    color: #999;
    font-weight: 400;
    white-space: nowrap;
    margin-left: 8px;
}

.groupId {
    font-weight: 500;
    margin-right: 4px;
}

/* 更多操作按钮 */
.moreButton {
    position: absolute;
    top: 0;
    right: 0;
    width: 24px;
    height: 24px;
    border-radius: 6px;
    border: none;
    background: transparent;
    color: #999;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.2s ease;

    &:hover {
        background: #f5f5f5;
        color: #333;
    }

    :global(.anticon) {
        font-size: 14px;
    }
}

/* 卡片悬停时显示更多按钮 */
.knowledgeCard:hover .moreButton {
    opacity: 1;
}

/* 下拉菜单样式 */
:global(.ant-dropdown) {
    .ant-dropdown-menu {
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        border: 1px solid #f0f0f0;
        padding: 4px 0;

        .ant-dropdown-menu-item {
            padding: 8px 12px;
            font-size: 14px;
            border-radius: 4px;
            margin: 2px 4px;

            &:hover {
                background: #f5f5f5;
                color: #333;
            }

            &.ant-dropdown-menu-item-danger {
                &:hover {
                    background: rgba(255, 77, 79, 0.08);
                    color: #ff4d4f;
                }
            }

            &.ant-dropdown-menu-item-disabled {
                color: #d9d9d9;
                cursor: not-allowed;

                &:hover {
                    background: transparent;
                    color: #d9d9d9;
                }
            }

            .anticon {
                margin-right: 8px;
                font-size: 14px;
            }
        }
    }
}

/* 分页 */
.pagination {
    display: flex;
    justify-content: center;
    margin-top: 32px;

    :global(.ant-pagination) {
        .ant-pagination-item {
            border-color: rgba(96, 92, 229, 0.2);
            border-radius: 8px;

            &:hover {
                border-color: #605ce5;
                color: #605ce5;
            }

            &.ant-pagination-item-active {
                background: #605ce5;
                border-color: #605ce5;
                color: #ffffff;
            }
        }

        .ant-pagination-prev,
        .ant-pagination-next {
            border-color: rgba(96, 92, 229, 0.2);
            border-radius: 8px;

            &:hover {
                border-color: #605ce5;
                color: #605ce5;
            }
        }

        .ant-pagination-jump-prev,
        .ant-pagination-jump-next {
            &:hover {
                color: #605ce5;
            }
        }
    }
}

/* 弹窗样式 */
.knowledgeModal {
    :global(.ant-modal-header) {
        border-bottom: 1px solid #f0f4f8;
        padding: 20px 24px 16px;

        .ant-modal-title {
            font-size: 18px;
            font-weight: 600;
            color: #1a1a1a;
        }
    }

    :global(.ant-modal-body) {
        padding: 24px;
    }

    :global(.ant-modal-close) {
        top: 16px;
        right: 16px;
    }
}

.knowledgeForm {
    :global(.ant-form-item-label) {
        label {
            font-weight: 500;
            color: #333;
        }
    }

    :global(.ant-input),
    :global(.ant-input-number),
    :global(.ant-select-selector) {
        border-radius: 8px;
        border-color: #e8e8e8;

        &:hover {
            border-color: #666;
        }

        &:focus,
        &.ant-select-focused .ant-select-selector {
            border-color: #666;
            box-shadow: none;
        }
    }

    :global(.ant-select-multiple) {
        .ant-select-selection-item {
            background: #f5f5f5;
            border-color: #e8e8e8;
            color: #333;
        }
    }
}

.formActions {
    margin-top: 32px;
    margin-bottom: 0;
    text-align: right;

    :global(.ant-form-item-control-input-content) {
        display: flex;
        gap: 12px;
        justify-content: flex-end;
    }

    :global(.ant-btn) {
        height: 36px;
        padding: 0 20px;
        border-radius: 8px;
        font-weight: 500;

        &.ant-btn-primary {
            background: #605ce5;
            border-color: #605ce5;
            box-shadow: 0 2px 8px rgba(96, 92, 229, 0.2);

            &:hover {
                background: #4c46d6;
                border-color: #4c46d6;
                box-shadow: 0 4px 12px rgba(96, 92, 229, 0.3);
            }
        }
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .fixedHeader {
        padding: 16px 16px 0 16px;
    }

    .scrollableContent {
        padding: 16px;
    }

    .cardGrid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .searchBar {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .searchInput {
        max-width: 100%;
    }

    .workGroupSelect {
        width: 100% !important;
        margin-left: 0 !important;
    }
}
