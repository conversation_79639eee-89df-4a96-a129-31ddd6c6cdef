import { useMemo } from 'react';
import debounce from 'lodash/debounce';
import { useLatest } from './useLatest';
import { useUnmount } from './useUnmount';
// 防抖 用法 useDebounceFn(fn, { wait: 1000 })
// 多次触发 → 只执行最后一次
export function useDebounceFn(fn, options) {
    const fnRef = useLatest(fn);
    const wait = options?.wait ?? 1000;

    const debounced = useMemo(() => debounce((...args) => fnRef.current(...args), wait, options), []);

    useUnmount(() => {
        debounced.cancel();
    });

    return {
        run: debounced,
        cancel: debounced.cancel,
        flush: debounced.flush
    };
}
